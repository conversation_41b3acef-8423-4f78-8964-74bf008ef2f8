use std::env;
use tracing::{debug, error, info, trace, warn};
use tracing_subscriber::EnvFilter;

pub fn setup_logging(app_name: &str, additional_crates: &[&str]) {
    let log_level = env::var("LOG_LEVEL").unwrap_or_else(|_| "info".to_string());

    let mut filter_string = format!("{app_name}={log_level},utils={log_level}");
    for &crate_name in additional_crates {
        filter_string.push(',');
        filter_string.push_str(&format!("{crate_name}={log_level}"));
    }

    let filter =
        EnvFilter::try_new(filter_string).unwrap_or_else(|_| EnvFilter::new(format!("{app_name}=info,utils=info")));

    tracing_subscriber::fmt().with_env_filter(filter).init();

    error!("Error messages enabled");
    warn!("Warning messages enabled");
    info!("Info messages enabled");
    debug!("Debug messages enabled");
    trace!("Trace messages enabled");
}
