use database_service::db::Database;
use database_service::grpc::character_db_service_server::CharacterDbServiceServer;
use database_service::grpc::database_service::MyDatabaseService;
use database_service::grpc::session_service_server::SessionServiceServer;
use database_service::grpc::user_service_server::UserServiceServer;
use dotenv::dotenv;
use sqlx::postgres::PgPoolOptions;
use std::env;
use std::net::SocketAddr;
use std::str::FromStr;
use std::sync::Arc;
use tokio::sync::{Mutex, oneshot};
use tokio::time::{timeout, Duration};
use tonic::transport::Server;
use tracing::{info, error, warn, Level};
use tracing_subscriber::EnvFilter;
use utils::logging;
use utils::redis_cache::RedisCache;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    dotenv().ok();
    let app_name = env!("CARGO_PKG_NAME");
    logging::setup_logging(app_name, &["database_service"]);

    let addr = env::var("LISTEN_ADDR").unwrap_or_else(|_| "0.0.0.0".to_string());
    let port = env::var("SERVICE_PORT").unwrap_or_else(|_| "50052".to_string());

    let database_url = env::var("DATABASE_URL").expect("DATABASE_URL must be set");
    let redis_url = env::var("REDIS_URL").unwrap_or_else(|_| "redis://127.0.0.1:6379".to_string());

    let pool = PgPoolOptions::new()
        .max_connections(5)
        .connect(&database_url)
        .await
        .expect("Failed to create PostgreSQL connection pool");

    let redis_cache = Arc::new(Mutex::new(RedisCache::new(&redis_url)));
    let db = Arc::new(Database::new(pool, redis_cache));
    let my_service = MyDatabaseService { db };

    // Start gRPC server with graceful shutdown support
    let (mut health_reporter, health_service) = tonic_health::server::health_reporter();
    health_reporter
        .set_serving::<UserServiceServer<MyDatabaseService>>()
        .await;

    let address = SocketAddr::new(addr.parse()?, port.parse()?);

    // Create shutdown signal channel
    let (shutdown_tx, shutdown_rx) = oneshot::channel::<()>();

    let server_task = tokio::spawn(async move {
        let server = Server::builder()
            .add_service(health_service)
            .add_service(UserServiceServer::new(my_service.clone()))
            .add_service(CharacterDbServiceServer::new(my_service.clone()))
            .add_service(SessionServiceServer::new(my_service))
            .serve_with_shutdown(address, async {
                shutdown_rx.await.ok();
                info!("Database service gRPC server shutdown signal received");
            });

        if let Err(e) = server.await {
            error!("Database service gRPC server error: {}", e);
        } else {
            info!("Database service gRPC server shut down gracefully");
        }
    });

    info!("Database Service running on {}", address);

    // Wait for shutdown signal
    info!("Database service is running. Waiting for shutdown signal...");
    utils::signal_handler::wait_for_signal().await;

    info!("Shutdown signal received. Beginning graceful shutdown...");

    // Signal the gRPC server to stop accepting new connections
    if let Err(_) = shutdown_tx.send(()) {
        warn!("Failed to send shutdown signal to gRPC server (receiver may have been dropped)");
    }

    // Wait for the gRPC server to finish with a timeout
    match timeout(Duration::from_secs(30), server_task).await {
        Ok(result) => {
            if let Err(e) = result {
                error!("Database service gRPC server task failed: {}", e);
            } else {
                info!("Database service shut down successfully");
            }
        }
        Err(_) => {
            error!("Database service gRPC server shutdown timed out after 30 seconds");
        }
    }

    Ok(())
}
