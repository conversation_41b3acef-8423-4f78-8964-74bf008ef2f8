#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct ComputedValues {
    pub run_speed: f32, // Movement speed in units per second
    pub walk_speed: f32,
    pub attack_speed: f32,
}

impl Default for ComputedValues {
    fn default() -> Self {
        Self { 
            run_speed: 100.0, // Default speed: 100 units per second
            walk_speed: 50.0,
            attack_speed: 1.0,
        }
    }
}

impl ComputedValues {
    pub fn new(run_speed: f32, walk_speed: f32, attack_speed: f32) -> Self {
        Self { run_speed, walk_speed, attack_speed }
    }
}
