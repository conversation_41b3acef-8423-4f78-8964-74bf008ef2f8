use std::sync::{Arc, Mutex};
use std::collections::HashMap;
use std::time::{Duration, Instant};
use hecs::{Entity, World};
use tracing::debug;
use crate::components::basic_info::BasicInfo;
use crate::components::position::Position;
use crate::components::markers::*;
use crate::components::life::Life;
use crate::spatial_grid::SpatialGrid;
use crate::id_manager::IdManager;
use crate::loader::Zone;
use crate::world_client::WorldGameLogicServiceImpl;

/// EntitySystem manages the game world and provides thread-safe access to entity operations
pub struct EntitySystem {
    world: Arc<Mutex<World>>,
    spatial_grid: Arc<Mutex<SpatialGrid>>,
    id_manager: Arc<Mutex<IdManager>>,
    player_positions: Arc<Mutex<HashMap<String, Position>>>, // session_id -> position
    world_service: Option<Arc<WorldGameLogicServiceImpl>>,
    last_spawner_update: Arc<Mutex<Instant>>,
    last_movement_update: Arc<Mutex<Instant>>,
}

impl EntitySystem {
    pub fn new() -> Self {
        let now = Instant::now();
        Self {
            world: Arc::new(Mutex::new(World::new())),
            spatial_grid: Arc::new(Mutex::new(SpatialGrid::new())),
            id_manager: Arc::new(Mutex::new(IdManager::new())),
            player_positions: Arc::new(Mutex::new(HashMap::new())),
            world_service: None,
            last_spawner_update: Arc::new(Mutex::new(now)),
            last_movement_update: Arc::new(Mutex::new(now)),
        }
    }

    pub fn with_world_service(mut self, world_service: Arc<WorldGameLogicServiceImpl>) -> Self {
        self.world_service = Some(world_service);
        self
    }

    /// Load a map zone into the world
    pub fn load_map(&self, zone: Zone) {
        let mut world = self.world.lock().unwrap();
        let mut spatial_grid = self.spatial_grid.lock().unwrap();
        let id_manager = self.id_manager.clone();

        // Create a temporary factory to load the map
        let mut temp_factory = crate::entity_factory::EntityFactory {
            world: &mut *world,
            spatial_grid: std::mem::take(&mut *spatial_grid),
            id_manager,
            world_service: self.world_service.clone(),
        };

        temp_factory.load_map(zone);

        // Put the spatial grid back
        *spatial_grid = temp_factory.spatial_grid;
        debug!("Map loaded into EntitySystem");
    }

    /// Run the entity system update (spawners, spatial grid, etc.)
    pub fn run(&self) {
        let now = Instant::now();

        // Check if we should update movement (every 50ms like C++ code)
        let should_update_movement = {
            let mut last_movement_update = self.last_movement_update.lock().unwrap();
            if now.duration_since(*last_movement_update) >= Duration::from_millis(50) {
                *last_movement_update = now;
                true
            } else {
                false
            }
        };

        let mut world = self.world.lock().unwrap();
        let mut spatial_grid = self.spatial_grid.lock().unwrap();
        let id_manager = self.id_manager.clone();

        // Create a temporary factory to run updates
        let mut temp_factory = crate::entity_factory::EntityFactory {
            world: &mut *world,
            spatial_grid: std::mem::take(&mut *spatial_grid),
            id_manager,
            world_service: self.world_service.clone(),
        };

        // Always update spawners - they have their own timing with spawn_rate
        temp_factory.update_spawners();

        // Always update spatial grid
        temp_factory.update_spatial_grid();

        // Update movement every 50ms like C++ code
        if should_update_movement {
            temp_factory.update_movement(50); // 50ms delta time
        }

        // Put the spatial grid back
        *spatial_grid = temp_factory.spatial_grid;
    }

    /// Get nearby objects at a specific position
    pub fn get_nearby_objects_at_position(&self, x: f32, y: f32, z: f32, map_id: u16) -> Vec<EntityInfo> {
        let world = self.world.lock().unwrap();
        let spatial_grid = self.spatial_grid.lock().unwrap();

        // Create a temporary position for the query
        let query_position = Position {
            x,
            y,
            z,
            map_id,
            spawn_id: 0,
        };

        // Get nearby entities using the spatial grid
        let nearby_entities = spatial_grid.get_nearby_entities(
            &*world,
            None, // No query entity to exclude
            &query_position,
            map_id
        );

        debug!("Found {} nearby entities at position ({}, {}, {}) on map {}",
               nearby_entities.len(), x, y, z, map_id);

        // Convert entities to EntityInfo
        let mut entity_infos = Vec::new();
        for entity in nearby_entities {
            if let Some(info) = self.entity_to_info(&*world, entity) {
                entity_infos.push(info);
            }
        }

        entity_infos
    }

    /// Convert an entity to EntityInfo with all relevant data
    fn entity_to_info(&self, world: &World, entity: Entity) -> Option<EntityInfo> {
        // Get position (required)
        let position = world.get::<&Position>(entity).ok()?;
        
        // Get basic info (required)
        let basic_info = world.get::<&BasicInfo>(entity).ok()?;
        
        // Get life info for HP
        let (hp, max_hp) = if let Ok(life) = world.get::<&Life>(entity) {
            (life.get_hp() as i32, life.get_max_hp() as i32)
        } else {
            (100, 100)
        };

        // Determine entity type based on marker components
        let entity_type = if world.get::<&Player>(entity).is_ok() {
            EntityType::Player
        } else if world.get::<&Npc>(entity).is_ok() {
            EntityType::Npc
        } else if world.get::<&Mob>(entity).is_ok() {
            EntityType::Mob
        } else {
            return None; // Skip entities without recognized types
        };

        Some(EntityInfo {
            id: basic_info.id as i32,
            entity_type,
            x: position.x,
            y: position.y,
            z: position.z,
            name: basic_info.name.clone(),
            hp,
            max_hp,
        })
    }

    /// Get a clone of the world for read-only operations
    pub fn get_world(&self) -> Arc<Mutex<World>> {
        self.world.clone()
    }

    /// Update player position and check for nearby entity changes
    pub fn update_player_position(&self, session_id: &str, x: f32, y: f32, z: f32, map_id: u16) {
        let new_pos = Position {
            x,
            y,
            z,
            map_id,
            spawn_id: 0,
        };

        // Get the old position if it exists
        let old_pos = {
            let mut player_positions = self.player_positions.lock().unwrap();
            let old_pos = player_positions.get(session_id).cloned();
            player_positions.insert(session_id.to_string(), new_pos.clone());
            old_pos
        };

        // If we have an old position, check for nearby entity changes
        if let Some(old_pos) = old_pos {
            // Only check if the player actually moved significantly
            let distance_moved = ((new_pos.x - old_pos.x).powi(2) + (new_pos.y - old_pos.y).powi(2)).sqrt();
            if distance_moved > 10.0 { // Only check if moved more than 10 units
                let mut world = self.world.lock().unwrap();
                let mut spatial_grid = self.spatial_grid.lock().unwrap();
                let id_manager = self.id_manager.clone();

                // Create a temporary factory to handle position updates
                let mut temp_factory = crate::entity_factory::EntityFactory {
                    world: &mut *world,
                    spatial_grid: std::mem::take(&mut *spatial_grid),
                    id_manager,
                    world_service: self.world_service.clone(),
                };

                temp_factory.update_player_position(session_id, old_pos, new_pos);

                // Put the spatial grid back
                *spatial_grid = temp_factory.spatial_grid;
            }
        } else {
            // First time seeing this player, just store their position
            debug!("First position update for player {}: ({}, {}, {})", session_id, x, y, z);
        }
    }

    /// Remove player position tracking when they disconnect
    pub fn remove_player_position(&self, session_id: &str) {
        let mut player_positions = self.player_positions.lock().unwrap();
        player_positions.remove(session_id);
        debug!("Removed position tracking for player {}", session_id);
    }
}

/// Information about an entity that can be sent to clients
#[derive(Debug, Clone)]
pub struct EntityInfo {
    pub id: i32,
    pub entity_type: EntityType,
    pub x: f32,
    pub y: f32,
    pub z: f32,
    pub name: String,
    pub hp: i32,
    pub max_hp: i32,
}

/// Type of entity
#[derive(Debug, Clone)]
pub enum EntityType {
    Player = 1,
    Npc = 2,
    Mob = 3,
}
