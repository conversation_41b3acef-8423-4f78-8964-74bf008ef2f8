// src/loader.rs

use serde::Deserialize;
use serde_json::Value;
use std::{
    error::Error,
    fs::File,
    io::BufReader,
};
use tracing::info;

/// A 3D vector type used for positions, scales, etc.
#[derive(Debug, Deserialize)]
pub struct Vec3 {
    #[serde(rename = "X")]
    pub x: f64,
    #[serde(rename = "Y")]
    pub y: f64,
    #[serde(rename = "Z")]
    pub z: f64,
}

/// Top-level zone definition.
#[derive(Debug, Deserialize)]
pub struct Zone {
    #[serde(rename = "Id")]
    pub id: u32,
    #[serde(rename = "SpawnPoints")]
    pub spawn_points: Option<Vec<SpawnPoint>>,
    #[serde(rename = "NpcSpawnPoints")]
    pub npc_spawn_points: Option<Vec<NpcSpawnPoint>>,
    #[serde(rename = "MobSpawnPoints")]
    pub mob_spawn_points: Option<Vec<MobSpawnPoint>>,
    #[serde(rename = "WarpPoints")]
    pub warp_points: Option<Vec<WarpPoint>>,
}

/// A simple spawn point with a type and a position.
#[derive(Debug, Deserialize)]
pub struct SpawnPoint {
    #[serde(rename = "Type")]
    pub point_type: u32,
    #[serde(rename = "Position")]
    pub position: Vec3,
}

/// An NPC spawn point.
#[derive(Debug, Deserialize)]
pub struct NpcSpawnPoint {
    #[serde(rename = "Id")]
    pub id: u32,
    #[serde(rename = "DialogId")]
    pub dialog_id: u32,
    #[serde(rename = "Position")]
    pub position: Vec3,
    #[serde(rename = "Angle")]
    pub angle: f32,
}

#[derive(Debug, Deserialize)]
pub struct SpawnPointDefinition {
    #[serde(rename = "Name")]
    pub name: String,
    #[serde(rename = "Monster")]
    pub monster_id: u32,
    #[serde(rename = "Count")]
    pub count: u32,
}

/// A mob spawn point.
#[derive(Debug, Deserialize)]
pub struct MobSpawnPoint {
    #[serde(rename = "SpawnName")]
    pub spawn_name: String,
    #[serde(rename = "NormalSpawnPoints")]
    pub normal_spawn_points: Option<Vec<SpawnPointDefinition>>,
    #[serde(rename = "TacticalSpawnPoints")]
    pub tactical_spawn_points: Option<Vec<SpawnPointDefinition>>,
    #[serde(rename = "Interval")]
    pub interval: u32,
    #[serde(rename = "Limit")]
    pub limit: u32,
    #[serde(rename = "Range")]
    pub range: u32,
    #[serde(rename = "TacticalVariable")]
    pub tactical_variable: u32,
    #[serde(rename = "Name")]
    pub name: String,
    #[serde(rename = "ObjectType")]
    pub object_type: u32,
    #[serde(rename = "ObjectID")]
    pub object_id: u32,
    #[serde(rename = "MapPosition")]
    pub map_position: Option<MapPosition>,
    #[serde(rename = "Position")]
    pub position: Vec3,
    #[serde(rename = "Rotation")]
    pub rotation: Rotation,
    #[serde(rename = "Scale")]
    pub scale: Vec3,
}

/// A simple map position.
#[derive(Debug, Deserialize)]
pub struct MapPosition {
    #[serde(rename = "X")]
    pub x: i32,
    #[serde(rename = "Y")]
    pub y: i32,
}

/// A simple rotation structure.
#[derive(Debug, Deserialize)]
pub struct Rotation {
    #[serde(rename = "W")]
    pub w: f64,
}

/// A warp point.
#[derive(Debug, Deserialize)]
pub struct WarpPoint {
    #[serde(rename = "Alias")]
    pub alias: String,
    #[serde(rename = "DestinationGateId")]
    pub destination_gate_id: u32,
    #[serde(rename = "Destination")]
    pub destination: Vec3,
    #[serde(rename = "MapId")]
    pub map_id: u32,
    #[serde(rename = "MinPosition")]
    pub min_position: Vec3,
    #[serde(rename = "MaxPosition")]
    pub max_position: Vec3,
}

/// Loads only the zone (from a JSON file) with a top-level `Id` matching the given `map_id`.
///
/// The JSON file is assumed to be an array of zones.
pub fn load_zone_for_map(
    file_path: &str,
    map_id: u32,
) -> Result<Option<Zone>, Box<dyn Error>> {
    let file = File::open(file_path)?;
    let reader = BufReader::new(file);
    // Deserialize the file as a JSON value first.
    let value: Value = serde_json::from_reader(reader)?;
    // Ensure the top-level element is an array.
    let zones = value
        .as_array()
        .ok_or("Expected JSON array at top level")?;

    for zone_value in zones {
        // Deserialize each zone.
        let zone: Zone = serde_json::from_value(zone_value.clone())?;
        if zone.id == map_id {
            return Ok(Some(zone));
        }
    }
    Ok(None)
}