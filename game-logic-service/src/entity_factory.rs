use std::cmp::min;
use hecs::{Entity, World};
use std::time::{Duration, Instant};
use std::sync::{Arc, Mutex};
use tracing::debug;
use crate::components::basic_info::BasicInfo;
use crate::components::level::Level;
use crate::components::life::Life;
use crate::components::position::Position;
use crate::components::spawner::Spawner;
use crate::components::destination::Destination;
use crate::components::computed_values::ComputedValues;
use crate::components::markers::*;
use crate::id_manager::IdManager;
use crate::loader::{MobSpawnPoint, Vec3, Zone};
use crate::random::{get_random_number_in_range, get_random_point_in_circle};
use crate::spatial_grid::SpatialGrid;
use crate::world_client::WorldGameLogicServiceImpl;

pub struct EntityFactory<'a> {
    pub world: &'a mut World,
    pub id_manager: Arc<Mutex<IdManager>>,
    pub spatial_grid: SpatialGrid,
    pub world_service: Option<Arc<WorldGameLogicServiceImpl>>,
}

impl<'a> EntityFactory<'a> {
    /// Creates a new factory.
    pub fn new(world: &'a mut World) -> Self {
        Self {
            world,
            id_manager: Arc::new(Mutex::new(IdManager::new())),
            spatial_grid: SpatialGrid::new(),
            world_service: None,
        }
    }

    pub fn with_world_service(mut self, world_service: Arc<WorldGameLogicServiceImpl>) -> Self {
        self.world_service = Some(world_service);
        self
    }
    
    pub fn load_map(&mut self, map: Zone) {
        // Load the map data from the database
        // Spawn all the entities in the map

        if let Some(mob_spawn_points) = map.mob_spawn_points {
            for spawner in mob_spawn_points {
                // Process the mob spawner.
                spawner
                    .normal_spawn_points
                    .unwrap_or_default()
                    .into_iter()
                    .chain(spawner.tactical_spawn_points.unwrap_or_default().into_iter())
                    .for_each(|spawn_point| {
                        // debug!("Spawn Point: {:?}", spawn_point);
                        self.spawn_mob_spawner(
                            spawn_point.monster_id,
                            spawner.limit,
                            spawn_point.count,
                            Position {
                                x: spawner.position.x as f32,
                                y: spawner.position.y as f32,
                                z: spawner.position.z as f32,
                                map_id: map.id as u16,
                                spawn_id: 0,
                            },
                            Duration::from_secs(spawner.interval as u64),
                            spawner.range,
                        );
                    });
            }
        }

        if let Some(npc_spawn_points) = map.npc_spawn_points {
            for npc in npc_spawn_points {
                // Process the npc spawn point.
                // debug!("NPC ID: {}", npc.id);
                // debug!("Dialog ID: {}", npc.dialog_id);
                // debug!("Position: x = {}, y = {}, z = {}",
                //          npc.position.x, npc.position.y, npc.position.z);
                self.spawn_npc(npc.id, Position {
                    x: npc.position.x as f32,
                    y: npc.position.y as f32,
                    z: npc.position.z as f32,
                    map_id: map.id as u16,
                    spawn_id: 0,
                },
                npc.angle);
            }
        }

        if let Some(spawn_points) = map.spawn_points {
            for spawn_point in spawn_points {
                // Process the spawn point.
                // debug!("Player Spawn Point Type: {}", spawn_point.point_type);
                // debug!("Position: x = {}, y = {}, z = {}",
                //          spawn_point.position.x, spawn_point.position.y, spawn_point.position.z);
                self.create_player_spawn_point(spawn_point.point_type, Position {
                    x: spawn_point.position.x as f32,
                    y: spawn_point.position.y as f32,
                    z: spawn_point.position.z as f32,
                    map_id: map.id as u16,
                    spawn_id: 0,
                });
            }
        }

        if let Some(warp_points) = map.warp_points {
            for warp_point in warp_points {
                // Process the warp point.
                // debug!("Warp Point Alias: {}", warp_point.alias);
                // debug!("Destination Gate ID: {}", warp_point.destination_gate_id);
                // debug!("Destination: x = {}, y = {}, z = {}",
                //          warp_point.destination.x, warp_point.destination.y, warp_point.destination.z);
                // debug!("Map ID: {}", warp_point.map_id);
                // debug!("Min Position: x = {}, y = {}, z = {}",
                //          warp_point.min_position.x, warp_point.min_position.y, warp_point.min_position.z);
                // debug!("Max Position: x = {}, y = {}, z = {}",
                //          warp_point.max_position.x, warp_point.max_position.y, warp_point.max_position.z);
            }
        }
    }
    
    pub fn create_player_spawn_point(&mut self, point_type: u32, pos: Position) {
        self.world.spawn((PlayerSpawn {point_type},pos.clone()));
        debug!("Player spawn point created at position: {:?}", pos);
    }
    
    pub fn spawn_player(&mut self, pos: Position) {
        let id = self.id_manager.lock().unwrap().get_free_id();
        let basic_info = BasicInfo {
            name: "Player".to_string(),
            id: id,
            tag: id as u32,
            ..Default::default()
        };
        let level = Level { level: 1, ..Default::default() };
        
        let base_hp = 100;
        let hp = (base_hp * level.level) as u32;
        let life = Life::new(hp, hp);
        
        let entity = self.world.spawn((Player, basic_info, level, life, pos.clone()));

        // Add entity to spatial grid
        self.spatial_grid.add_entity(entity, &pos);

        debug!("Player spawned at position: {:?}", pos);
    }

    /// Spawns a npc at the specified position.
    pub fn spawn_npc(&mut self, npc_id: u32, pos: Position, angle: f32) {
        let id = self.id_manager.lock().unwrap().get_free_id();
        let basic_info = BasicInfo {
            name: "NPC".to_string(),
            id: id,
            tag: id as u32,
            ..Default::default()
        };
        let level = Level { level: 1, ..Default::default() };
        
        let base_hp = 100;
        let hp = (base_hp * level.level) as u32;
        let life = Life::new(hp, hp);
        
        let entity = self.world.spawn((Npc {id: npc_id, quest_id: 0, angle, event_status: 0}, basic_info, level, life, pos.clone()));

        // Add entity to spatial grid
        self.spatial_grid.add_entity(entity, &pos);

        // Send event to nearby clients about the new NPC
        self.send_npc_spawn_event_to_nearby_clients(npc_id, &pos, angle);

        debug!("Npc spawned at position: {:?}", pos);
    }

    /// Spawns a mob at the specified position.
    pub fn spawn_mob(&mut self, mob_id: u32, spawn_range: u32, pos: Position) -> Entity {
        let id = self.id_manager.lock().unwrap().get_free_id();
        let basic_info = BasicInfo {
            name: "MOB".to_string(),
            id: id,
            tag: id as u32,
            ..Default::default()
        };

        let level = Level { level: 1, ..Default::default() };

        let base_hp = 100;
        let hp = (base_hp * level.level) as u32;
        let life = Life::new(hp, hp);

        let (x, y) = get_random_point_in_circle((pos.x, pos.y), spawn_range as f32);
        let spawn_point = Position { x, y, z: pos.z, map_id: pos.map_id, spawn_id: pos.spawn_id };

        // Spawn the mob.
        let entity = self.world.spawn((Mob {id: mob_id, quest_id: 0}, basic_info, level, life, spawn_point.clone()));

        // Add entity to spatial grid
        self.spatial_grid.add_entity(entity, &spawn_point);

        // Send event to nearby clients about the new mob
        self.send_mob_spawn_event_to_nearby_clients(id.into(), &spawn_point, mob_id);

        entity
    }

    /// Spawns a spawner at the specified position with the given spawn rate.
    pub fn spawn_mob_spawner(&mut self, mob_id: u32, max_mob_count: u32, max_spawn_count_at_once: u32, pos: Position, spawn_rate: Duration, spawn_range: u32) {
        let spawner = Spawner {
            mob_id,
            spawn_rate,
            spawn_range,
            max_mob_count,
            max_spawn_count_at_once,
            ..Default::default()
        };
        self.world.spawn((spawner, pos.clone()));
    }

    /// Updates all spawner entities in the world.
    ///
    /// If the spawn interval has elapsed, a mob will be spawned and the spawner's
    /// last spawn timestamp is updated.
    pub fn update_spawners(&mut self) {
        let now = Instant::now();

        // Collect spawner entities to avoid borrow issues.
        // We need to clone the Position since we use it after.
        let spawner_data: Vec<(hecs::Entity, Position, Spawner)> = self
            .world
            .query::<(&Position, &Spawner)>()
            .iter()
            .map(|(entity, (pos, spawner))| {
                (
                    entity,
                    pos.clone(),
                    Spawner {
                        mob_id: spawner.mob_id,
                        max_mob_count: spawner.max_mob_count,
                        max_spawn_count_at_once: spawner.max_spawn_count_at_once,
                        spawn_rate: spawner.spawn_rate,
                        last_spawn: spawner.last_spawn,
                        spawn_range: spawner.spawn_range,
                        mobs: spawner.mobs.clone(),
                        ..Default::default()
                    },
                )
            })
            .collect();

        // Iterate over each spawner and check if it's time to spawn a mob.
        for (entity, pos, spawner) in spawner_data {
            let mut mob_list = spawner.mobs.clone();
            if mob_list.len() >= spawner.max_mob_count as usize {
                continue;
            }
            if now.duration_since(spawner.last_spawn) >= spawner.spawn_rate {
                let spawn_count = get_random_number_in_range(0, min(spawner.max_spawn_count_at_once, (spawner.max_mob_count - spawner.mobs.len() as u32)));
                for _ in 0..spawn_count {
                    let mob_entity = self.spawn_mob(spawner.mob_id, spawner.spawn_range, pos.clone());
                    
                    // Add the mob to the spawner's list of mobs.
                    mob_list.push(mob_entity);
                    
                    // Event is already sent in spawn_mob method
                }
                
                // Update the spawner's last_spawn time in the world.
                let mut query = self.world.query_one::<(&Position, &mut Spawner)>(entity).unwrap();
                let (_pos, spawner_mut) = query.get().unwrap(); 
                spawner_mut.last_spawn = now;
                spawner_mut.mobs = mob_list;
            }
        }
    }
    
    pub fn get_nearby_objects(&self, id: Entity) -> Vec<Entity> {
        debug!("Getting nearby objects for entity {:?}", id);

        // Get the position of the query entity
        if let Ok(position) = self.world.get::<&Position>(id) {
            // Use spatial grid to find nearby entities
            let nearby_entities = self.spatial_grid.get_nearby_entities(
                self.world,
                Some(id), // Exclude the query entity itself
                &*position,
                position.map_id
            );

            debug!("Found {} nearby objects for entity {:?} at position ({}, {}, {}) on map {}",
                   nearby_entities.len(), id, position.x, position.y, position.z, position.map_id);

            nearby_entities
        } else {
            debug!("Entity {:?} has no position component", id);
            vec![]
        }
    }
    
    pub fn run(&mut self) {
        self.update_spawners();
        self.update_spatial_grid();
    }

    /// Updates entity movement based on destinations (like C++ code)
    /// This should be called every 50ms
    pub fn update_movement(&mut self, dt_ms: u64) {
        let dt = Duration::from_millis(dt_ms);

        // Collect entities with Position, Destination, and ComputedValues
        let moving_entities: Vec<(Entity, Position, Destination, ComputedValues)> = self
            .world
            .query::<(&Position, &Destination, &ComputedValues)>()
            .iter()
            .map(|(entity, (pos, dest, values))| {
                (entity, pos.clone(), dest.clone(), values.clone())
            })
            .collect();

        for (entity, mut pos, mut dest, values) in moving_entities {
            let speed = values.run_speed;
            let ntime_ms = if dest.dist > 0.0 {
                (1000.0 * dest.dist / speed) as u64
            } else {
                0
            };
            let ntime = Duration::from_millis(ntime_ms);

            let dx = dest.x - pos.x;
            let dy = dest.y - pos.y;
            let distance = (dx * dx + dy * dy).sqrt();
            dest.dist = distance;

            if ntime <= dt || distance <= 0.0001 {
                // Reached destination
                self.update_entity_position(entity, dest.x, dest.y, pos.z);

                // Remove destination component
                let _ = self.world.remove_one::<Destination>(entity);

                // TODO: check_for_target equivalent
                debug!("Entity {:?} reached destination ({}, {})", entity, dest.x, dest.y);
            } else {
                // Move towards destination
                let progress = dt.as_millis() as f32 / ntime.as_millis() as f32;
                let new_x = pos.x + dx * progress;
                let new_y = pos.y + dy * progress;

                self.update_entity_position(entity, new_x, new_y, pos.z);
            }
        }
    }

    /// Update an entity's position and spatial grid
    fn update_entity_position(&mut self, entity: Entity, x: f32, y: f32, z: f32) {
        if let Ok(mut position) = self.world.get::<&mut Position>(entity) {
            let old_pos = position.clone();
            position.x = x;
            position.y = y;
            position.z = z;

            // Update spatial grid
            self.spatial_grid.update_entity_position(entity, &old_pos, &*position);
        }
    }

    /// Updates the spatial grid with current entity positions
    /// This should be called periodically to keep the grid in sync
    pub fn update_spatial_grid(&mut self) {
        // For now, we rebuild the entire grid each time
        // In a more optimized implementation, we would track position changes
        // and only update entities that have moved
        self.spatial_grid.rebuild_from_world(self.world);
    }

    /// Send mob spawn event to nearby clients
    fn send_mob_spawn_event_to_nearby_clients(&self, mob_id: u32, position: &Position, npc_id: u32) {
        if let Some(world_service) = &self.world_service {
            let event = world_service.create_mob_spawn_event(mob_id, position.x, position.y, npc_id);

            // Send the event asynchronously
            let world_service_clone = world_service.clone();
            tokio::spawn(async move {
                if let Err(e) = world_service_clone.send_event(event).await {
                    debug!("Failed to send mob spawn event: {}", e);
                }
            });
        }
    }

    /// Send NPC spawn event to nearby clients
    fn send_npc_spawn_event_to_nearby_clients(&self, npc_id: u32, position: &Position, angle: f32) {
        if let Some(world_service) = &self.world_service {
            let event = world_service.create_npc_spawn_event(npc_id, position.x, position.y, angle);

            // Send the event asynchronously
            let world_service_clone = world_service.clone();
            tokio::spawn(async move {
                if let Err(e) = world_service_clone.send_event(event).await {
                    debug!("Failed to send NPC spawn event: {}", e);
                }
            });
        }
    }

    /// Update player position and check for nearby entity changes
    pub fn update_player_position(&mut self, session_id: &str, old_pos: Position, new_pos: Position) {
        // Find the player entity by session_id (we'll need to add session_id to player components)
        // For now, we'll implement a basic version that checks for nearby changes

        // Get entities that were nearby at old position
        let old_nearby = self.spatial_grid.get_nearby_entities(
            self.world,
            None,
            &old_pos,
            old_pos.map_id
        );

        // Get entities that are nearby at new position
        let new_nearby = self.spatial_grid.get_nearby_entities(
            self.world,
            None,
            &new_pos,
            new_pos.map_id
        );

        // Find entities that entered range (in new_nearby but not in old_nearby)
        let entered_range: Vec<Entity> = new_nearby.iter()
            .filter(|entity| !old_nearby.contains(entity))
            .cloned()
            .collect();

        // Find entities that left range (in old_nearby but not in new_nearby)
        let left_range: Vec<Entity> = old_nearby.iter()
            .filter(|entity| !new_nearby.contains(entity))
            .cloned()
            .collect();

        // Send spawn events for entities that entered range
        for entity in entered_range {
            self.send_entity_spawn_event_to_client(session_id, entity);
        }

        // Send despawn events for entities that left range
        for entity in left_range {
            self.send_entity_despawn_event_to_client(session_id, entity);
        }
    }

    /// Send entity spawn event to a specific client
    fn send_entity_spawn_event_to_client(&self, session_id: &str, entity: Entity) {
        if let Some(world_service) = &self.world_service {
            // Get entity information
            if let (Ok(position), Ok(basic_info)) = (
                self.world.get::<&Position>(entity),
                self.world.get::<&BasicInfo>(entity)
            ) {
                // Determine entity type and send appropriate event
                if self.world.get::<&Mob>(entity).is_ok() {
                    if let Ok(mob) = self.world.get::<&Mob>(entity) {
                        let event = world_service.create_mob_spawn_event_for_client(
                            session_id,
                            basic_info.id.into(),
                            position.x,
                            position.y,
                            mob.id
                        );

                        let world_service_clone = world_service.clone();
                        tokio::spawn(async move {
                            if let Err(e) = world_service_clone.send_event(event).await {
                                debug!("Failed to send mob spawn event to client: {}", e);
                            }
                        });
                    }
                } else if self.world.get::<&Npc>(entity).is_ok() {
                    if let Ok(npc) = self.world.get::<&Npc>(entity) {
                        let event = world_service.create_npc_spawn_event_for_client(
                            session_id,
                            basic_info.id.into(),
                            position.x,
                            position.y,
                            npc.angle
                        );

                        let world_service_clone = world_service.clone();
                        tokio::spawn(async move {
                            if let Err(e) = world_service_clone.send_event(event).await {
                                debug!("Failed to send NPC spawn event to client: {}", e);
                            }
                        });
                    }
                }
                // Note: We don't send player spawn events to avoid showing other players for now
            }
        }
    }

    /// Send entity despawn event to a specific client
    fn send_entity_despawn_event_to_client(&self, session_id: &str, entity: Entity) {
        if let Some(world_service) = &self.world_service {
            if let Ok(basic_info) = self.world.get::<&BasicInfo>(entity) {
                let event = world_service.create_object_despawn_event_for_client(
                    session_id,
                    basic_info.id.into()
                );

                let world_service_clone = world_service.clone();
                tokio::spawn(async move {
                    if let Err(e) = world_service_clone.send_event(event).await {
                        debug!("Failed to send object despawn event to client: {}", e);
                    }
                });
            }
        }
    }
}