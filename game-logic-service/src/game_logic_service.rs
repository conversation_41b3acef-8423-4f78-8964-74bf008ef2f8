use futures::{Stream, StreamExt};
use std::collections::HashMap;
use std::pin::Pin;
use std::sync::{Arc, Mutex};
use tonic::{Request, Response, Status};
use tonic::metadata::MetadataMap;
use tracing::debug;
use crate::entity_system::{EntitySystem, EntityType};

pub mod game_logic {
    tonic::include_proto!("game_logic");
}

use game_logic::game_logic_service_server::GameLogicService;
use crate::game_logic_service::game_logic::{NearbyObjectsRequest, NearbyObjectsResponse, Object};

pub struct MyGameLogicService {
    pub map_id: u32,
    pub entity_system: Arc<EntitySystem>,
}

#[tonic::async_trait]
impl GameLogicService for MyGameLogicService {
    async fn get_nearby_objects(&self, request: Request<NearbyObjectsRequest>) -> Result<Response<NearbyObjectsResponse>, Status> {
        let req = request.into_inner();
        debug!("GetNearbyObjects request: {:?}", req);

        // Get nearby entities from the entity system
        let entity_infos = self.entity_system.get_nearby_objects_at_position(
            req.x,
            req.y,
            req.z,
            req.map_id as u16,
        );

        // Convert EntityInfo to proto Object
        let objects: Vec<Object> = entity_infos
            .into_iter()
            .map(|info| Object {
                id: info.id,
                r#type: info.entity_type as i32,
                x: info.x,
                y: info.y,
                z: info.z,
                hp: info.hp,
                max_hp: info.max_hp,
            })
            .collect();

        debug!("Returning {} nearby objects", objects.len());

        let response = NearbyObjectsResponse { objects };
        Ok(Response::new(response))
    }
}