use tonic::{Request, transport::Channel};
use futures::StreamExt;
use tokio::sync::{mpsc, Mutex};
use tokio_stream::wrappers::ReceiverStream;
use std::error::Error;
use tracing::{debug, error};

mod character_common {
    tonic::include_proto!("character_common");
}

pub mod world {
    tonic::include_proto!("game");
}

use world::event_service_client::EventServiceClient;
use world::GenericEvent;
use crate::interceptors::auth_interceptor::AuthInterceptor;

/// WorldClientHandler encapsulates the bidirectional event stream.
/// In addition to providing an API to send messages, it also spawns a
/// background task which forwards incoming chat messages through an inbound channel.
pub struct WorldClientHandler {
    outbound_tx: mpsc::Sender<GenericEvent>,
    /// Inbound messages from the chat service are sent here.
    pub inbound_rx: Mutex<mpsc::Receiver<GenericEvent>>,
}

impl WorldClientHandler {
    /// Creates and returns a new WorldClientHandler.
    ///
    /// * `world_url` - Full URL of the World Service (for example, "http://127.0.0.1:50051")
    /// * `client_id` - The authenticated client ID to be injected into each request.
    /// * `session_id` - The authenticated session token to be injected into each request.
    pub async fn new(
        world_url: String,
        client_id: String,
        session_id: String,
    ) -> Result<Self, Box<dyn Error + Send + Sync>> {
        // Create a channel to the World Service.
        let channel = Channel::from_shared(world_url)?.connect().await
            .map_err(|e| Box::new(e) as Box<dyn Error + Send + Sync>)?;
        let interceptor = AuthInterceptor { client_id, session_id };

        // Create EventService client with interceptor.
        let mut world_client = EventServiceClient::with_interceptor(channel, interceptor);

        // Create an mpsc channel for outbound messages.
        let (out_tx, out_rx) = mpsc::channel(32);
        let outbound_stream = ReceiverStream::new(out_rx);

        // This channel will be used to forward inbound messages to the packet-service.
        let (in_tx, in_rx) = mpsc::channel(32);

        // Establish the bidirectional chat stream.
        let request = Request::new(outbound_stream);
        let mut response = world_client.stream_events(request).await
            .map_err(|e| Box::new(e) as Box<dyn Error + Send + Sync>)?.into_inner();

        // Spawn a task to continuously receive messages from the Chat Service.
        // Each received message is sent through the 'in_tx' channel.
        tokio::spawn(async move {
            while let Some(result) = response.next().await {
                match result {
                    Ok(event) => {
                        // Process the event as needed.
                        // debug!("Received event: {:?}", event);
                        if let Err(e) = in_tx.send(event).await {
                            error!("Failed to forward event: {:?}", e);
                            break;
                        }
                    }
                    Err(e) => {
                        error!("Error receiving event stream message: {:?}", e);
                        break;
                    }
                }
            }
            debug!("Event inbound stream closed");
        });

        Ok(Self {
            outbound_tx: out_tx,
            inbound_rx: Mutex::new(in_rx),
        })
    }
}