use auth_service::auth::auth_service_server::AuthServiceServer;
use auth_service::database_client::DatabaseClient;
use auth_service::database_client::DatabaseClientTrait;
use auth_service::grpc::MyAuthService;
use auth_service::session::session_service_client::SessionServiceClient;
use dotenv::dotenv;
use std::env;
use std::sync::Arc;
use tokio::sync::oneshot;
use tokio::time::{timeout, Duration};
use tonic::transport::Server;
use tracing::{info, error, warn};
use utils::logging;
use utils::service_discovery::get_kube_service_endpoints_by_dns;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Load environment variables from .env
    dotenv().ok();

    let app_name = env!("CARGO_PKG_NAME");
    logging::setup_logging(app_name, &["auth_service"]);

    // Set the gRPC server address
    let addr = env::var("LISTEN_ADDR").unwrap_or_else(|_| "0.0.0.0".to_string());
    let port = env::var("SERVICE_PORT").unwrap_or_else(|_| "50051".to_string());
    let db_url = format!(
        "http://{}",
        get_kube_service_endpoints_by_dns("database-service", "tcp", "database-service")
            .await?
            .get(0)
            .unwrap()
    );

    let db_client = Arc::new(DatabaseClient::connect(&db_url).await?);
    let session_client = Arc::new(SessionServiceClient::connect(db_url).await?);

    let full_addr = format!("{}:{}", &addr, port);
    let address = full_addr.parse().expect("Invalid address");
    let auth_service = MyAuthService {
        db_client,
        session_client,
    };

    // Start gRPC server with graceful shutdown support
    let (mut health_reporter, health_service) = tonic_health::server::health_reporter();
    health_reporter.set_serving::<AuthServiceServer<MyAuthService>>().await;

    // Create shutdown signal channel
    let (shutdown_tx, shutdown_rx) = oneshot::channel::<()>();

    let server_task = tokio::spawn(async move {
        let server = Server::builder()
            .add_service(health_service)
            .add_service(AuthServiceServer::new(auth_service))
            .serve_with_shutdown(address, async {
                shutdown_rx.await.ok();
                info!("Auth service gRPC server shutdown signal received");
            });

        if let Err(e) = server.await {
            error!("Auth service gRPC server error: {}", e);
        } else {
            info!("Auth service gRPC server shut down gracefully");
        }
    });

    info!("Authentication Service running on {}", addr);

    // Wait for shutdown signal
    info!("Auth service is running. Waiting for shutdown signal...");
    utils::signal_handler::wait_for_signal().await;

    info!("Shutdown signal received. Beginning graceful shutdown...");

    // Signal the gRPC server to stop accepting new connections
    if let Err(_) = shutdown_tx.send(()) {
        warn!("Failed to send shutdown signal to gRPC server (receiver may have been dropped)");
    }

    // Wait for the gRPC server to finish with a timeout
    match timeout(Duration::from_secs(30), server_task).await {
        Ok(result) => {
            if let Err(e) = result {
                error!("Auth service gRPC server task failed: {}", e);
            } else {
                info!("Auth service shut down successfully");
            }
        }
        Err(_) => {
            error!("Auth service gRPC server shutdown timed out after 30 seconds");
        }
    }

    Ok(())
}
