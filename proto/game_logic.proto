syntax = "proto3";

package game_logic;

service GameLogicService {
  rpc GetNearbyObjects(NearbyObjectsRequest) returns (NearbyObjectsResponse);
}

message NearbyObjectsRequest {
  string session_id = 1;
  float x = 2;
  float y = 3;
  float z = 4;
  int32 map_id = 5;
}

message NearbyObjectsResponse {
  repeated Object objects = 1;
}

message Object {
  int32 id = 1;
  int32 type_ = 2;
  float x = 3;
  float y = 4;
  float z = 5;
  int32 hp = 6;
  int32 max_hp = 7;
}