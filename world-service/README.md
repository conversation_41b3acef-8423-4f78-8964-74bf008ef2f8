# World Service

The World Service manages the game world state and character interactions in the MMORPG server architecture.

## Overview

The World Service is responsible for:
- Managing character positions and movement
- Handling map changes
- Processing character interactions
- Managing NPCs, monsters, and objects
- Handling combat and skills

## Architecture

The service is built using the following components:

- **gRPC Server**: Exposes world management endpoints
- **World State Manager**: Maintains the current state of the game world
- **Map Manager**: Handles map data and transitions
- **Entity Manager**: Manages characters, NPCs, and monsters

## Service Endpoints

The World Service exposes the following gRPC endpoints:

### GetCharacter
Retrieves a character's world state.

```protobuf
rpc GetCharacter(CharacterRequest) returns (CharacterResponse);
```

### ChangeMap
Handles a character changing maps.

```protobuf
rpc ChangeMap(ChangeMapRequest) returns (ChangeMapResponse);
```

### MoveCharacter
Updates a character's position.

```protobuf
rpc MoveCharacter(CharacterMoveRequest) returns (CharacterMoveResponse);
```

### GetTargetHp
Retrieves an object's current HP.

```protobuf
rpc GetTargetHp(ObjectHpRequest) returns (ObjectHpResponse);
```

## World Data Structure

The world consists of:

- **Maps**: Different areas with unique IDs
- **Spawn Points**: Locations where characters can appear
- **NPCs**: Non-player characters with fixed positions
- **Monsters**: Hostile entities that can move and attack
- **Objects**: Interactive items in the world

## Configuration

The service can be configured using environment variables:

- `LISTEN_ADDR`: The address to listen on (default: "0.0.0.0")
- `SERVICE_PORT`: The port to listen on (default: "50054")
- `LOG_LEVEL`: Logging level (default: "info")
- `MAP_IDS`: Comma-separated list of map IDs to manage (e.g., "42,43,44,45")
- `WORLD_SERVICE_NAME`: Name of the world service instance (default: "default-service")

### Game Logic Connection Retry Configuration

The world service includes comprehensive retry logic for connecting to game logic instances that may not be ready immediately:

#### Connection Info Retrieval Retry
- `CONNECTION_INFO_MAX_RETRIES`: Maximum number of retry attempts for getting pod connection info (default: 3)
- `CONNECTION_INFO_INITIAL_DELAY_MS`: Initial delay between connection info retries in milliseconds (default: 2000)
- `CONNECTION_INFO_MAX_DELAY_MS`: Maximum delay between connection info retries in milliseconds (default: 10000)

#### gRPC Client Connection Retry
- `GAME_LOGIC_MAX_RETRIES`: Maximum number of retry attempts for gRPC connections (default: 5)
- `GAME_LOGIC_INITIAL_DELAY_MS`: Initial delay between gRPC connection retries in milliseconds (default: 500)
- `GAME_LOGIC_MAX_DELAY_MS`: Maximum delay between gRPC connection retries in milliseconds (default: 10000)

Both retry mechanisms use exponential backoff, doubling the delay after each failed attempt up to the maximum delay. The service will continue starting even if some game logic instances fail to connect, allowing for partial functionality.

### Kubernetes Service Creation

The world service automatically creates both Pods and Services for each game logic instance:

- **Pod**: Contains the game logic service container
- **Service**: Provides stable networking and service discovery
  - Type: ClusterIP
  - Port: 50056 (gRPC)
  - Selector: Matches specific instance by app, map_id, and instance labels

The world service connects to game logic instances using Kubernetes Service DNS names (e.g., `world-default-service-42-service.default.svc.cluster.local`) instead of direct Pod IPs, providing better reliability and following Kubernetes best practices.

## Running the Service

### Local Development

```bash
cargo run
```

### Docker

```bash
docker build -t world-service .
docker run -p 50054:50054 world-service
```

## Integration with External Systems

The World Service integrates with:

- **Database Service**: For retrieving and storing world state
- **Auth Service**: For user authentication and authorization
- **Character Service**: For character data
- **Packet Service**: For handling client requests related to the world
